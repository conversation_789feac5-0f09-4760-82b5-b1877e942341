#include "midi.h"
#include "constants.h"
#include "file_utils.h"
#include "sysex.h"
#include <CoreMIDI/CoreMIDI.h>
#include <CoreFoundation/CoreFoundation.h>
#include <dispatch/dispatch.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

DroidError midi_context_init(MidiContext *context, const char *client_name, const char *port_name) {
    if (!context) {
        return DROID_ERROR_INVALID_ARGS;
    }

    // Initialize context
    memset(context, 0, sizeof(MidiContext));

    // Use default names if not provided
    const char *actual_client_name = client_name ? client_name : MIDI_CLIENT_NAME;
    const char *actual_port_name = port_name ? port_name : MIDI_OUTPUT_PORT_NAME;

    // Create MIDI client
    CFStringRef cf_client_name = CFStringCreateWithCString(NULL, actual_client_name, kCFStringEncodingUTF8);
    if (!cf_client_name) {
        return DROID_ERROR_MEMORY_ALLOCATION;
    }

    OSStatus status = MIDIClientCreate(cf_client_name, NULL, NULL, &context->client);
    CFRelease(cf_client_name);

    if (status != noErr) {
        return DROID_ERROR_MIDI_CLIENT_CREATION;
    }

    // Create output port
    CFStringRef cf_port_name = CFStringCreateWithCString(NULL, actual_port_name, kCFStringEncodingUTF8);
    if (!cf_port_name) {
        MIDIClientDispose(context->client);
        return DROID_ERROR_MEMORY_ALLOCATION;
    }

    status = MIDIOutputPortCreate(context->client, cf_port_name, &context->output_port);
    CFRelease(cf_port_name);

    if (status != noErr) {
        MIDIClientDispose(context->client);
        return DROID_ERROR_MIDI_PORT_CREATION;
    }

    context->is_initialized = true;
    return DROID_SUCCESS;
}

void midi_context_cleanup(MidiContext *context) {
    if (!context || !context->is_initialized) {
        return;
    }

    if (context->output_port) {
        MIDIPortDispose(context->output_port);
        context->output_port = 0;
    }

    if (context->client) {
        MIDIClientDispose(context->client);
        context->client = 0;
    }

    context->is_initialized = false;
}

MIDIEndpointRef midi_find_destination(const char *device_name) {
    if (!device_name) {
        return 0;
    }

    ItemCount destination_count = MIDIGetNumberOfDestinations();

    for (ItemCount i = 0; i < destination_count; i++) {
        MIDIEndpointRef endpoint = MIDIGetDestination(i);
        if (endpoint == 0) {
            continue;
        }

        char endpoint_name[MAX_DEVICE_NAME_LENGTH];
        DroidError name_error = midi_get_endpoint_name(endpoint, endpoint_name, sizeof(endpoint_name));

        if (name_error == DROID_SUCCESS && strstr(endpoint_name, device_name) != NULL) {
            return endpoint;
        }
    }

    return 0;
}

void midi_list_destinations(bool verbose) {
    ItemCount destination_count = MIDIGetNumberOfDestinations();

    printf("Available MIDI destinations (%u found):\n", (unsigned int)destination_count);

    if (destination_count == 0) {
        printf("  No MIDI destinations found.\n");
        return;
    }

    for (ItemCount i = 0; i < destination_count; i++) {
        MIDIEndpointRef endpoint = MIDIGetDestination(i);
        if (endpoint == 0) {
            continue;
        }

        char endpoint_name[MAX_DEVICE_NAME_LENGTH];
        DroidError name_error = midi_get_endpoint_name(endpoint, endpoint_name, sizeof(endpoint_name));

        if (name_error == DROID_SUCCESS) {
            printf("  %u: %s\n", (unsigned int)i, endpoint_name);

            if (verbose) {
                // Additional endpoint information could be added here
                printf("      Endpoint ID: %u\n", (unsigned int)endpoint);
            }
        } else {
            printf("  %u: <Unknown device>\n", (unsigned int)i);
        }
    }
}

void midi_sysex_completion_callback(MIDISysexSendRequest *request) {
    if (!request) {
        return;
    }

    SysExContext *context = (SysExContext *)request->completionRefCon;
    if (!context) {
        return;
    }

    // Free the buffer
    if (context->buffer) {
        free(context->buffer);
        context->buffer = NULL;
    }

    // Signal completion
    if (context->semaphore) {
        dispatch_semaphore_signal(context->semaphore);
    }

    // Free the context
    free(context);
}

DroidError midi_send_sysex(MIDIEndpointRef endpoint, const uint8_t *sysex_data,
                          size_t sysex_size, double timeout_seconds) {
    if (endpoint == 0 || !sysex_data || sysex_size == 0) {
        return DROID_ERROR_INVALID_ARGS;
    }

    // Validate the SysEx message
    DroidError validation_error = sysex_validate_message(sysex_data, sysex_size);
    if (validation_error != DROID_SUCCESS) {
        return validation_error;
    }

    // Validate endpoint
    DroidError endpoint_error = midi_validate_endpoint(endpoint);
    if (endpoint_error != DROID_SUCCESS) {
        return endpoint_error;
    }

    // Create semaphore for synchronization
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    if (!semaphore) {
        return DROID_ERROR_MEMORY_ALLOCATION;
    }

    // Allocate buffer for sending (MIDISendSysex takes ownership)
    uint8_t *send_buffer = malloc(sysex_size);
    if (!send_buffer) {
        dispatch_release(semaphore);
        return DROID_ERROR_MEMORY_ALLOCATION;
    }
    memcpy(send_buffer, sysex_data, sysex_size);

    // Create context for callback
    SysExContext *context = malloc(sizeof(SysExContext));
    if (!context) {
        free(send_buffer);
        dispatch_release(semaphore);
        return DROID_ERROR_MEMORY_ALLOCATION;
    }
    context->buffer = send_buffer;
    context->semaphore = semaphore;

    // Prepare SysEx request
    MIDISysexSendRequest request;
    memset(&request, 0, sizeof(request));
    request.destination = endpoint;
    request.data = send_buffer;
    request.bytesToSend = (UInt32)sysex_size;
    request.complete = false;
    request.completionProc = midi_sysex_completion_callback;
    request.completionRefCon = context;

    // Send the message
    OSStatus status = MIDISendSysex(&request);
    if (status != noErr) {
        free(send_buffer);
        free(context);
        dispatch_release(semaphore);
        return DROID_ERROR_MIDI_SEND_FAILED;
    }

    // Wait for completion
    dispatch_time_t timeout = (timeout_seconds > 0)
        ? dispatch_time(DISPATCH_TIME_NOW, (int64_t)(timeout_seconds * NSEC_PER_SEC))
        : DISPATCH_TIME_FOREVER;

    long wait_result = dispatch_semaphore_wait(semaphore, timeout);
    dispatch_release(semaphore);

    if (wait_result != 0) {
        // Timeout occurred - the callback should still clean up eventually
        return DROID_ERROR_MIDI_SEND_FAILED;
    }

    return DROID_SUCCESS;
}

DroidError midi_validate_endpoint(MIDIEndpointRef endpoint) {
    if (endpoint == 0) {
        return DROID_ERROR_MIDI_DEVICE_NOT_FOUND;
    }

    // Try to get a property to verify the endpoint is still valid
    CFStringRef name = NULL;
    OSStatus status = MIDIObjectGetStringProperty(endpoint, kMIDIPropertyName, &name);

    if (status != noErr) {
        return DROID_ERROR_MIDI_DEVICE_NOT_FOUND;
    }

    if (name) {
        CFRelease(name);
    }

    return DROID_SUCCESS;
}

DroidError midi_get_endpoint_name(MIDIEndpointRef endpoint, char *name_buffer, size_t buffer_size) {
    if (endpoint == 0 || !name_buffer || buffer_size == 0) {
        return DROID_ERROR_INVALID_ARGS;
    }

    CFStringRef cf_name = NULL;
    OSStatus status = MIDIObjectGetStringProperty(endpoint, kMIDIPropertyName, &cf_name);

    if (status != noErr || !cf_name) {
        return DROID_ERROR_MIDI_DEVICE_NOT_FOUND;
    }

    Boolean success = CFStringGetCString(cf_name, name_buffer, buffer_size, kCFStringEncodingUTF8);
    CFRelease(cf_name);

    return success ? DROID_SUCCESS : DROID_ERROR_MIDI_DEVICE_NOT_FOUND;
}

DroidError midi_check_availability(void) {
    // Check if we can get the number of destinations
    ItemCount count = MIDIGetNumberOfDestinations();
    (void)count; // Suppress unused variable warning

    // If we get here without crashing, MIDI is available
    return DROID_SUCCESS;
}
